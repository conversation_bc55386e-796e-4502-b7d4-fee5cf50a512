import React, { Component } from 'react'
import FullCalendar from '@fullcalendar/react'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import interactionPlugin from '@fullcalendar/interaction'

export class Calendar extends Component {
  calendarComponentRef = React.createRef();
  constructor() {
    super();
    this.state = {
      calendarWeekends: true,
      calendarEvents: [
        { title: 'Audit', start: new Date() },
        { id: 2, title: 'Inspection: Warehouse 12', start: new Date(2023, 10, 20, 9), end: new Date(2023, 10, 20, 11) },
        { id: 3, title: 'Audit: Finance Department', start: new Date(2023, 10, 22, 13), end: new Date(2023, 10, 22, 15) },

      ]
    }
  }
  render() {
    return (
      <div>
        <div className="row">
          <div className="col-md-3">
            <div className="fc-external-events">
              <div className='fc-event'>
                <p>Inspection: Warehouse 12</p>
                <p className="small-text"></p>
                <p className="text-muted mb-0">By Samson</p>
              </div>
              <div className='fc-event'>
                <p>Audit: Finance Department</p>
                <p className="small-text"></p>
                <p className="text-muted mb-0">By Adhi</p>
              </div>

            </div>
            <div className="mt-4">
              <p>Filter</p>
              <div className="form-check form-check-primary">
                <label className="form-check-label">
                  <input type="checkbox" className="form-check-input" defaultChecked />
                  <i className="input-helper"></i>
                  Inspection
                </label>
              </div>
              <div className="form-check form-check-danger">
                <label className="form-check-label">
                  <input type="checkbox" className="form-check-input" defaultChecked />
                  <i className="input-helper"></i>
                  Audit
                </label>
              </div>
              <div className="form-check form-check-info">
                <label className="form-check-label">
                  <input type="checkbox" className="form-check-input" defaultChecked />
                  <i className="input-helper"></i>
                  High Priority
                </label>
              </div>
              <div className="form-check form-check-success">
                <label className="form-check-label">
                  <input type="checkbox" className="form-check-input" defaultChecked />
                  <i className="input-helper"></i>
                  Low Priority
                </label>
              </div>
            </div>
          </div>
          <div className="col-md-9">
            <div className="card">
              <div className="card-body">
                <h4 className="card-title"></h4>
                {/* <FullCalendar defaultView="dayGridMonth" plugins={[ dayGridPlugin ]} /> */}
                <FullCalendar
                  defaultView="dayGridMonth"
                  header={{
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
                  }}
                  plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
                  ref={this.calendarComponentRef}
                  weekends={this.state.calendarWeekends}
                  events={this.state.calendarEvents}
                  dateClick={this.handleDateClick}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  handleDateClick = (arg) => {
    if (window.confirm('Would you like to create a inspection and audit task to ' + arg.dateStr + ' ?')) {
      this.setState({  // add new event data
        calendarEvents: this.state.calendarEvents.concat({ // creates a new array
          title: 'New Task',
          start: arg.date,
          allDay: arg.allDay
        })
      })
    }
  }
}

export default Calendar
