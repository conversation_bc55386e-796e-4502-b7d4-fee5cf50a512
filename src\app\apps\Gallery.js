import React, { useState } from 'react';
import Gallery from "react-photo-gallery";
import Carousel, { Modal, ModalGateway } from "react-images";

// const photos = [
//   {
//     src: require("../../assets/images/samples/1280x768/1.jpg"),
//     width: 4,
//     height: 3
//   }
// ];

const GalleryPage = (props) => {

  
const [currentImage, setCurrentImage] = useState(0);
const [viewerIsOpen, setViewerIsOpen] = useState(false);



  const openLightbox = (event, { photo, index }) => {
   
    setCurrentImage(index)
    setViewerIsOpen(true)
  }

  const closeLightbox = () => {
   
    setCurrentImage(0)
    setViewerIsOpen(false)
  }

  return (
    <div>
      <div >
        <div >
          <div className="card">
            <div className="card-body">
           
              <Gallery photos={props.photos} onClick={openLightbox} />
              <ModalGateway>
                {viewerIsOpen ? (
                  <Modal onClose={closeLightbox}>
                    <Carousel
                      currentIndex={currentImage}
                      views={props.photos.map(x => ({
                        ...x,
                        srcset: x.srcSet,
                        caption: x.title
                      }))}
                    />
                  </Modal>
                ) : null}
              </ModalGateway>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

}

export default GalleryPage
